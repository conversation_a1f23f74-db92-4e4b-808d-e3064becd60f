# Simple syntax test
try {
    $scriptContent = Get-Content "scripts\windows\start-services-windows.ps1" -Raw
    $errors = $null
    $tokens = [System.Management.Automation.PSParser]::Tokenize($scriptContent, [ref]$errors)
    
    if ($errors.Count -eq 0) {
        Write-Host "✅ No syntax errors found!" -ForegroundColor Green
    } else {
        Write-Host "❌ Syntax errors found:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "Line $($error.Token.StartLine): $($error.Message)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "❌ Error checking syntax: $($_.Exception.Message)" -ForegroundColor Red
}
