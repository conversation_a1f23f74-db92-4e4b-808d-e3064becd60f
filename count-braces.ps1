# Count braces in the script
$content = Get-Content "scripts\windows\start-services-windows.ps1"
$openBraces = 0
$closeBraces = 0
$lineNumber = 0

foreach ($line in $content) {
    $lineNumber++
    $openCount = ($line.ToCharArray() | Where-Object { $_ -eq '{' }).Count
    $closeCount = ($line.ToCharArray() | Where-Object { $_ -eq '}' }).Count
    
    $openBraces += $openCount
    $closeBraces += $closeCount
    
    if ($openCount -gt 0 -or $closeCount -gt 0) {
        $balance = $openBraces - $closeBraces
        Write-Host "Line $lineNumber`: +$openCount -$closeCount (balance: $balance) - $($line.Trim())"
    }
}

Write-Host ""
Write-Host "Total: $openBraces open braces, $closeBraces close braces"
if ($openBraces -eq $closeBraces) {
    Write-Host "✅ Braces are balanced!" -ForegroundColor Green
} else {
    Write-Host "❌ Braces are NOT balanced! Missing $($openBraces - $closeBraces) closing braces" -ForegroundColor Red
}
