{"time":"2025-06-27T09:41:34.851Z","level":"INFO","message":"Service health check completed","total":3,"healthy":1,"unhealthy":2}
{"time":"2025-06-27T09:41:34.856Z","level":"INFO","message":"✅ Ollama is healthy","service":"Ollama","url":"http://localhost:11434/api/tags","responseTime":64}
{"time":"2025-06-27T09:41:34.858Z","level":"ERROR","message":"❌ ChromaDB is unhealthy","service":"ChromaDB","url":"http://localhost:8000/api/v1/heartbeat","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T09:41:34.859Z","level":"ERROR","message":"❌ MeiliSearch is unhealthy","service":"MeiliSearch","url":"http://localhost:7700/health","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T09:41:40.723Z","level":"INFO","message":"Summarizer service initialized successfully"}
{"time":"2025-06-27T09:41:40.723Z","level":"INFO","message":"Recommender service initialized successfully"}
{"time":"2025-06-27T09:41:40.728Z","level":"INFO","message":"Initializing object recognizer service..."}
{"time":"2025-06-27T09:41:40.733Z","level":"INFO","message":"🔍 Performing startup health check..."}
{"time":"2025-06-27T09:41:40.733Z","level":"ERROR","message":"Failed to initialize scene detector service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-27T09:41:40.733Z","level":"ERROR","message":"Failed to initialize audio analyzer service","error":"FFmpeg not found: ENOENT: no such file or directory, uv_spawn 'ffmpeg'"}
{"time":"2025-06-27T09:41:41.151Z","level":"WARN","message":"Failed to load MobileNet model, using fallback approach","error":"Failed to parse model JSON of response from https://tfhub.dev/google/tfjs-model/imagenet/mobilenet_v2_100_224/classification/3/default/1. Please make sure the server is serving valid JSON for this request."}
{"time":"2025-06-27T09:41:41.152Z","level":"INFO","message":"Object recognizer service initialized successfully","modelType":"mock"}
{"time":"2025-06-27T09:41:42.772Z","level":"INFO","message":"Service health check completed","total":3,"healthy":1,"unhealthy":2}
{"time":"2025-06-27T09:41:42.773Z","level":"INFO","message":"✅ Ollama is healthy","service":"Ollama","url":"http://localhost:11434/api/tags","responseTime":13}
{"time":"2025-06-27T09:41:42.774Z","level":"ERROR","message":"❌ ChromaDB is unhealthy","service":"ChromaDB","url":"http://localhost:8000/api/v1/heartbeat","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T09:41:42.775Z","level":"ERROR","message":"❌ MeiliSearch is unhealthy","service":"MeiliSearch","url":"http://localhost:7700/health","error":"Unable to connect. Is the computer able to access the url?"}
{"time":"2025-06-27T09:41:42.776Z","level":"WARN","message":"⚠️ 2 of 3 services are unhealthy. Please start missing services.","unhealthyServices":["ChromaDB","MeiliSearch"]}
{"time":"2025-06-27T09:41:42.777Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-27T09:41:42.778Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\processing"}
{"time":"2025-06-27T09:41:42.778Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\archive"}
{"time":"2025-06-27T09:41:42.778Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\error"}
{"time":"2025-06-27T09:41:42.778Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\outputs"}
{"time":"2025-06-27T09:41:42.778Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\logs"}
{"time":"2025-06-27T09:41:42.779Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\dashboard"}
{"time":"2025-06-27T09:41:42.779Z","level":"INFO","message":"Ensured folder exists","folder":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\media"}
{"time":"2025-06-27T09:41:42.798Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:42.798Z","level":"INFO","message":"🔄 Running database migrations..."}
{"time":"2025-06-27T09:41:42.801Z","level":"INFO","message":"✅ Migration 001 (Normalize Dependencies) already applied"}
{"time":"2025-06-27T09:41:42.800Z","level":"INFO","message":"🚀 Starting migration runner..."}
{"time":"2025-06-27T09:41:42.801Z","level":"INFO","message":"✅ Migration 003 (Add Periodic Tasks) already applied"}
{"time":"2025-06-27T09:41:42.801Z","level":"INFO","message":"✅ Migration 005 (Add Search Analytics) already applied"}
{"time":"2025-06-27T09:41:42.801Z","level":"INFO","message":"✅ Migration 002 (Add Retry System) already applied"}
{"time":"2025-06-27T09:41:42.801Z","level":"INFO","message":"✅ Migration 004 (Add Error Message Column) already applied"}
{"time":"2025-06-27T09:41:42.802Z","level":"INFO","message":"✅ Migration 006 (Add Transcription Analytics) already applied"}
{"time":"2025-06-27T09:41:42.802Z","level":"INFO","message":"Running migration 008: Add Phase 2 Advanced AI Features"}
{"time":"2025-06-27T09:41:42.802Z","level":"INFO","message":"🔄 Running migration 008: Add Phase 2 Advanced AI Features"}
{"time":"2025-06-27T09:41:42.802Z","level":"INFO","message":"✅ Migration 007 (Add Media Intelligence) already applied"}
{"time":"2025-06-27T09:41:42.823Z","level":"INFO","message":"Added summary columns to media_transcripts table"}
{"time":"2025-06-27T09:41:42.908Z","level":"INFO","message":"Migration 008 completed successfully"}
{"time":"2025-06-27T09:41:42.914Z","level":"INFO","message":"Running migration 009: Add autonomous learning tables"}
{"time":"2025-06-27T09:41:42.914Z","level":"INFO","message":"🔄 Running migration 009: Add Autonomous Learning and Optimization"}
{"time":"2025-06-27T09:41:42.914Z","level":"INFO","message":"✅ Migration 008 completed successfully"}
{"time":"2025-06-27T09:41:43.002Z","level":"INFO","message":"Migration 009 completed successfully"}
{"time":"2025-06-27T09:41:43.009Z","level":"INFO","message":"✅ Migration 009 completed successfully"}
{"time":"2025-06-27T09:41:43.009Z","level":"INFO","message":"🔄 Running migration 010: Add LLM-Based Planning System"}
{"time":"2025-06-27T09:41:43.010Z","level":"INFO","message":"Running migration 010: Add LLM-Based Planning System"}
{"time":"2025-06-27T09:41:43.091Z","level":"INFO","message":"✅ Migration 010 completed: LLM-Based Planning System tables created"}
{"time":"2025-06-27T09:41:43.095Z","level":"INFO","message":"✅ Migration 010 completed successfully"}
{"time":"2025-06-27T09:41:43.096Z","level":"INFO","message":"Running Rule Scheduler migration (011)..."}
{"time":"2025-06-27T09:41:43.095Z","level":"INFO","message":"🔄 Running migration 011: Add Rule Scheduler System"}
{"time":"2025-06-27T09:41:43.096Z","level":"INFO","message":"Creating activity_patterns table..."}
{"time":"2025-06-27T09:41:43.100Z","level":"INFO","message":"activity_patterns table created"}
{"time":"2025-06-27T09:41:43.100Z","level":"INFO","message":"Creating scheduling_rules table..."}
{"time":"2025-06-27T09:41:43.104Z","level":"INFO","message":"scheduling_rules table created"}
{"time":"2025-06-27T09:41:43.113Z","level":"INFO","message":"predictive_schedules table created"}
{"time":"2025-06-27T09:41:43.109Z","level":"INFO","message":"Creating predictive_schedules table..."}
{"time":"2025-06-27T09:41:43.113Z","level":"INFO","message":"Creating optimization_results table..."}
{"time":"2025-06-27T09:41:43.105Z","level":"INFO","message":"Creating rule_actions table..."}
{"time":"2025-06-27T09:41:43.109Z","level":"INFO","message":"rule_actions table created"}
{"time":"2025-06-27T09:41:43.226Z","level":"INFO","message":"Indexes created for rule scheduler tables"}
{"time":"2025-06-27T09:41:43.117Z","level":"INFO","message":"optimization_results table created"}
{"time":"2025-06-27T09:41:43.122Z","level":"INFO","message":"Adding rule scheduler columns to task_schedules table..."}
{"time":"2025-06-27T09:41:43.230Z","level":"INFO","message":"✅ Applied 4 migration(s) successfully"}
{"time":"2025-06-27T09:41:43.118Z","level":"INFO","message":"Creating user_behavior_profiles table..."}
{"time":"2025-06-27T09:41:43.122Z","level":"INFO","message":"user_behavior_profiles table created"}
{"time":"2025-06-27T09:41:43.145Z","level":"INFO","message":"Creating indexes for rule scheduler tables..."}
{"time":"2025-06-27T09:41:43.145Z","level":"INFO","message":"Rule scheduler columns added to task_schedules table"}
{"time":"2025-06-27T09:41:43.230Z","level":"INFO","message":"✅ Migration 011 completed successfully"}
{"time":"2025-06-27T09:41:43.230Z","level":"INFO","message":"✅ Database migrations completed"}
{"time":"2025-06-27T09:41:43.226Z","level":"INFO","message":"Rule Scheduler migration completed successfully"}
{"time":"2025-06-27T09:41:43.232Z","level":"ERROR","message":"Failed to create Meilisearch index","error":"Request to http://localhost:7700/indexes has failed","indexName":"media_index"}
{"time":"2025-06-27T09:41:43.238Z","level":"ERROR","message":"❌ Failed to initialize Meilisearch service","error":"Request to http://localhost:7700/indexes has failed"}
{"time":"2025-06-27T09:41:43.239Z","level":"INFO","message":"✅ Retry manager initialized"}
{"time":"2025-06-27T09:41:43.240Z","level":"INFO","message":"Task scheduler started","checkInterval":60000,"maxConcurrentInstances":10}
{"time":"2025-06-27T09:41:43.240Z","level":"INFO","message":"✅ Task scheduler initialized and started"}
{"time":"2025-06-27T09:41:43.240Z","level":"INFO","message":"🚀 Initializing MCP Manager with all servers..."}
{"time":"2025-06-27T09:41:43.242Z","level":"INFO","message":"MCP configuration loaded"}
{"time":"2025-06-27T09:41:43.519Z","level":"INFO","message":"51,57,55,32,124,32,32,32,32,32,32,32,32,32,32,32,32,32,97,119,97,105,116,32,108,111,103,103,101,114,46,119,97,114,110,40,39,70,97,105,108,101,100,32,116,111,32,112,97,114,115,101,32,74,83,79,78,32,102,114,111,109,32,76,76,77,32,114,101,115,112,111,110,115,101,44,32,117,115,105,110,103,32,102,97,108,108,98,97,99,107,39,44,32,123,32,101,114,114,111,114,32,125,41,10,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,94,10,101,114,114,111,114,58,32,34,97,119,97,105,116,34,32,99,97,110,32,111,110,108,121,32,98,101,32,117,115,101,100,32,105,110,115,105,100,101,32,97,110,32,34,97,115,121,110,99,34,32,102,117,110,99,116,105,111,110,10,32,32,32,32,97,116,32,67,58,92,67,111,100,101,92,98,97,110,97,110,97,45,98,117,110,92,115,114,99,92,115,101,114,118,105,99,101,115,92,108,108,109,45,112,108,97,110,110,105,110,103,45,115,101,114,118,105,99,101,46,116,115,58,51,57,55,58,49,57,10,10,51,56,50,32,124,32,32,32,32,32,112,114,105,118,97,116,101,32,112,97,114,115,101,80,108,97,110,82,101,115,112,111,110,115,101,40,114,101,115,112,111,110,115,101,58,32,115,116,114,105,110,103,41,58,32,71,101,110,101,114,97,116,101,100,80,108,97,110,32,123,10,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,94,10,110,111,116,101,58,32,67,111,110,115,105,100,101,114,32,97,100,100,105,110,103,32,116,104,101,32,34,97,115,121,110,99,34,32,107,101,121,119,111,114,100,32,104,101,114,101,10,32,32,32,97,116,32,67,58,92,67,111,100,101,92,98,97,110,97,110,97,45,98,117,110,92,115,114,99,92,115,101,114,118,105,99,101,115,92,108,108,109,45,112,108,97,110,110,105,110,103,45,115,101,114,118,105,99,101,46,116,115,58,51,56,50,58,49,51,10,10,51,57,55,32,124,32,32,32,32,32,32,32,32,32,32,32,32,32,97,119,97,105,116,32,108,111,103,103,101,114,46,119,97,114,110,40,39,70,97,105,108,101,100,32,116,111,32,112,97,114,115,101,32,74,83,79,78,32,102,114,111,109,32,76,76,77,32,114,101,115,112,111,110,115,101,44,32,117,115,105,110,103,32,102,97,108,108,98,97,99,107,39,44,32,123,32,101,114,114,111,114,32,125,41,59,10,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,94,10,101,114,114,111,114,58,32,85,110,101,120,112,101,99,116,101,100,32,46,10,32,32,32,32,97,116,32,67,58,92,67,111,100,101,92,98,97,110,97,110,97,45,98,117,110,92,115,114,99,92,115,101,114,118,105,99,101,115,92,108,108,109,45,112,108,97,110,110,105,110,103,45,115,101,114,118,105,99,101,46,116,115,58,51,57,55,58,50,53,10,10,66,117,110,32,118,49,46,50,46,49,52,32,40,87,105,110,100,111,119,115,32,120,54,52,41,10"}
{"time":"2025-06-27T09:41:43.584Z","level":"INFO","message":"80,97,116,116,101,114,110,32,65,110,97,108,121,115,105,115,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.585Z","level":"INFO","message":"80,97,116,116,101,114,110,32,65,110,97,108,121,115,105,115,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.580Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.589Z","level":"INFO","message":"77,101,105,108,105,83,101,97,114,99,104,32,77,67,80,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.589Z","level":"INFO","message":"77,111,110,105,116,111,114,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.591Z","level":"INFO","message":"77,101,105,108,105,83,101,97,114,99,104,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.585Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.594Z","level":"INFO","message":"77,101,100,105,97,32,73,110,116,101,108,108,105,103,101,110,99,101,32,77,67,80,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10,77,101,100,105,97,32,73,110,116,101,108,108,105,103,101,110,99,101,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.589Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.597Z","level":"INFO","message":"87,104,105,115,112,101,114,32,77,67,80,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.599Z","level":"INFO","message":"87,104,105,115,112,101,114,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.590Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.603Z","level":"INFO","message":"87,101,98,83,111,99,107,101,116,32,115,101,114,118,101,114,32,115,116,97,114,116,101,100,32,111,110,32,112,111,114,116,32,56,48,56,48,10"}
{"time":"2025-06-27T09:41:43.604Z","level":"INFO","message":"77,101,116,97,100,97,116,97,32,79,112,116,105,109,105,122,97,116,105,111,110,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.585Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.605Z","level":"INFO","message":"77,111,110,105,116,111,114,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.608Z","level":"INFO","message":"77,101,116,97,100,97,116,97,32,79,112,116,105,109,105,122,97,116,105,111,110,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.599Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.619Z","level":"INFO","message":"82,101,115,111,117,114,99,101,32,79,112,116,105,109,105,122,97,116,105,111,110,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.620Z","level":"INFO","message":"85,115,101,114,32,66,101,104,97,118,105,111,114,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.621Z","level":"INFO","message":"82,101,115,111,117,114,99,101,32,79,112,116,105,109,105,122,97,116,105,111,110,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.621Z","level":"INFO","message":"85,115,101,114,32,66,101,104,97,118,105,111,114,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.616Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.617Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.625Z","level":"INFO","message":"67,111,110,116,101,110,116,32,81,117,97,108,105,116,121,32,115,101,114,118,101,114,32,100,97,116,97,98,97,115,101,32,105,110,105,116,105,97,108,105,122,101,100,10"}
{"time":"2025-06-27T09:41:43.627Z","level":"INFO","message":"67,111,110,116,101,110,116,32,81,117,97,108,105,116,121,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:41:43.623Z","level":"INFO","message":"Database initialized successfully"}
{"time":"2025-06-27T09:41:43.674Z","level":"INFO","message":"67,104,114,111,109,97,68,66,32,77,67,80,32,115,101,114,118,101,114,32,114,117,110,110,105,110,103,32,111,110,32,115,116,100,105,111,10"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server chromadb","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server whisper","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server monitor","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server meilisearch","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server llm_planning","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.296Z","level":"ERROR","message":"Failed to start MCP server media_intelligence","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.297Z","level":"ERROR","message":"Failed to start MCP server metadata_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.298Z","level":"ERROR","message":"Failed to start MCP server: chromadb","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.298Z","level":"ERROR","message":"Failed to start MCP server: whisper","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.299Z","level":"ERROR","message":"Failed to start MCP server: monitor","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.299Z","level":"ERROR","message":"Failed to start MCP server: meilisearch","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.300Z","level":"ERROR","message":"Failed to start MCP server: llm_planning","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.300Z","level":"ERROR","message":"Failed to start MCP server: metadata_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.300Z","level":"ERROR","message":"Failed to start MCP server: media_intelligence","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.300Z","level":"ERROR","message":"Failed to initialize MCP Manager","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.303Z","level":"ERROR","message":"❌ Failed to initialize MCP Manager","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.310Z","level":"ERROR","message":"Failed to initialize embedding manager","error":"Failed to connect to Chroma"}
{"time":"2025-06-27T09:42:14.311Z","level":"ERROR","message":"Failed to initialize Enhanced Task Processor","error":"Failed to connect to Chroma"}
{"time":"2025-06-27T09:42:14.313Z","level":"ERROR","message":"❌ MCP fallback also failed","error":"Failed to connect to Chroma"}
{"time":"2025-06-27T09:42:14.313Z","level":"INFO","message":"🔄 Starting orchestrator loop..."}
{"time":"2025-06-27T09:42:14.315Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:14.323Z","level":"ERROR","message":"Failed to start MCP server resource_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.323Z","level":"ERROR","message":"Failed to start MCP server pattern_analysis","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.325Z","level":"ERROR","message":"Failed to start MCP server: resource_optimization","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.325Z","level":"ERROR","message":"Failed to start MCP server: pattern_analysis","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.341Z","level":"ERROR","message":"Failed to start MCP server content_quality","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.341Z","level":"ERROR","message":"Failed to start MCP server user_behavior","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.342Z","level":"ERROR","message":"Failed to start MCP server: content_quality","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.343Z","level":"ERROR","message":"Failed to start MCP server: user_behavior","error":"MCP request timeout for initialize"}
{"time":"2025-06-27T09:42:14.370Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:14.371Z","level":"INFO","message":"✅ Orchestrator loop started"}
{"time":"2025-06-27T09:42:14.372Z","level":"INFO","message":"File watcher started successfully"}
{"time":"2025-06-27T09:42:14.371Z","level":"INFO","message":"Starting file watcher","directory":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\incoming"}
{"time":"2025-06-27T09:42:14.373Z","level":"INFO","message":"Starting RSS watcher","feedCount":2,"checkInterval":3600}
{"time":"2025-06-27T09:42:14.374Z","level":"INFO","message":"✅ RSS watcher started successfully"}
{"time":"2025-06-27T09:42:14.373Z","level":"INFO","message":"Media file watcher started successfully"}
{"time":"2025-06-27T09:42:14.374Z","level":"DEBUG","message":"Checking RSS feeds","count":2}
{"time":"2025-06-27T09:42:14.372Z","level":"INFO","message":"Starting media file watcher","directory":"C:\\Users\\<USER>\\Sync\\AI Drop Box\\media"}
{"time":"2025-06-27T09:42:15.326Z","level":"ERROR","message":"Failed to check RSS feed","url":"https://example.com/podcast.xml","error":"HTTP 404: Not Found"}
{"time":"2025-06-27T09:42:15.789Z","level":"ERROR","message":"Failed to check RSS feed","url":"https://example.com/video-feed.xml","error":"HTTP 404: Not Found"}
{"time":"2025-06-27T09:42:19.382Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:19.417Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:24.393Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:24.425Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:29.399Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:29.429Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:34.399Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:34.434Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:39.407Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:39.436Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:44.412Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:44.443Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:49.419Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:49.454Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:54.419Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:54.450Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:42:59.419Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:42:59.449Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:04.427Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:04.479Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:09.421Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:09.452Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:14.426Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:14.461Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:19.427Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:19.462Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:24.435Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:24.468Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:29.433Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:29.468Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:34.483Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:34.543Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:39.450Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:39.490Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:44.452Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:44.483Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:49.448Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:49.481Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:54.463Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:54.493Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:43:59.466Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:43:59.500Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:04.483Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:04.528Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:09.465Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:09.498Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:14.468Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:14.502Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:19.464Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:19.506Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:24.463Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:24.496Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:29.463Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:29.501Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:34.465Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:34.499Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:39.465Z","level":"INFO","message":"🔄 Orchestrator loop starting..."}
{"time":"2025-06-27T09:44:39.498Z","level":"INFO","message":"✅ Orchestrator loop completed successfully"}
{"time":"2025-06-27T09:44:40.004Z","level":"INFO","message":"🛑 Shutting down services..."}
